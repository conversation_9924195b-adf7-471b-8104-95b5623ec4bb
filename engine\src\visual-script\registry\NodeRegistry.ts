/**
 * 视觉脚本节点注册系统
 * 统一管理所有节点的注册、分类和编辑器集成
 */
import { VisualScriptNode } from '../../visualscript/VisualScriptNode';
import { Debug } from '../../utils/Debug';

// 导入所有节点类
// 基础功能节点
import { 
  CameraInputNode, 
  PoseDetectionNode, 
  HandTrackingNode, 
  VirtualInteractionNode,
  FaceDetectionNode,
  BodyAnalysisNode,
  MotionProcessingNode
} from '../nodes/mocap/MocapNodes';

import {
  CreateEntityNode,
  DestroyEntityNode,
  FindEntityNode,
  CloneEntityNode,
  EntityStateNode
} from '../nodes/entity/EntityNodes';

import {
  AddComponentNode,
  RemoveComponentNode,
  GetComponentNode,
  HasComponentNode,
  EnableComponentNode,
  DisableComponentNode
} from '../nodes/component/ComponentNodes';

import {
  SetPositionNode,
  GetPositionNode,
  SetRotationNode,
  GetRotationNode,
  SetScaleNode,
  GetScaleNode,
  TransformNode,
  LookAtNode,
  MoveTowardsNode
} from '../nodes/transform/TransformNodes';

import {
  CreateRigidBodyNode,
  ApplyForceNode,
  ApplyImpulseNode,
  SetVelocityNode,
  CollisionDetectionNode,
  PhysicsConstraintNode
} from '../nodes/physics/PhysicsNodes';

import {
  PlayAnimationNode,
  StopAnimationNode
} from '../nodes/animation/AnimationNodes';

import {
  KeyboardInputNode,
  MouseInputNode,
  TouchInputNode,
  GamepadInputNode
} from '../nodes/input/InputNodes';

import {
  PlayAudioNode,
  StopAudioNode,
  SetVolumeNode,
  AudioAnalyzerNode
} from '../nodes/audio/AudioNodes';

// 3D世界构建节点
import {
  AutoSceneGenerationNode,
  SceneLayoutNode
} from '../nodes/scene/SceneGenerationNodes';

import {
  CreateWaterBodyNode,
  WaterWaveNode
} from '../nodes/water/WaterSystemNodes';

import {
  ParticleEmitterNode,
  ParticleEffectNode
} from '../nodes/particle/ParticleSystemNodes';

import {
  PostProcessEffectNode,
  ToneMappingNode
} from '../nodes/postprocess/PostProcessNodes';

import {
  TerrainGenerationNode,
  TerrainErosionNode
} from '../nodes/terrain/TerrainSystemNodes';

// 专业系统节点
import {
  WalletConnectNode,
  SmartContractNode,
  NFTOperationNode
} from '../nodes/blockchain/BlockchainNodes';

import {
  LearningRecordNode,
  LearningStatisticsNode,
  AchievementSystemNode
} from '../nodes/learning/LearningRecordNodes';

import {
  CreateUIElementNode,
  UILayoutNode,
  UIEventHandlerNode
} from '../nodes/ui/UINodes';

// AI增强节点
import {
  KnowledgeBaseNode,
  RAGQueryNode,
  DocumentProcessingNode,
  SemanticSearchNode
} from '../nodes/rag/RAGApplicationNodes';

import {
  GISAnalysisNode,
  SpatialQueryNode,
  GeospatialVisualizationNode,
  LocationServicesNode
} from '../nodes/spatial/SpatialInformationNodes';

// 新增的核心节点
import {
  WebSocketNode,
  WebRTCNode,
  HTTPRequestNode,
  NetworkSyncNode
} from '../nodes/network/NetworkNodes';

import {
  MaterialSystemNode,
  LightControlNode,
  CameraManagerNode,
  RenderConfigNode
} from '../nodes/rendering/RenderingNodes';

import {
  AnimationStateMachineNode,
  AnimationBlendNode,
  IKSystemNode,
  AnimationEventNode
} from '../nodes/animation/AdvancedAnimationNodes';

// 第六阶段新增节点
import {
  DeviceManagerNode,
  DataCollectionNode,
  QualityInspectionNode,
  AlarmSystemNode,
  ProcessControlNode
} from '../nodes/industrial/IndustrialAutomationNodes';

import {
  SpatialAudioNode,
  AudioFilterNode,
  AudioEffectNode
} from '../nodes/audio/AdvancedAudioNodes';

import {
  VRControllerNode,
  GestureRecognitionNode,
  VoiceRecognitionNode
} from '../nodes/input/VRInputNodes';

import {
  LODSystemNode,
  BatchRenderingNode,
  InstancedRenderingNode,
  FrustumCullingNode
} from '../nodes/rendering/RenderingOptimizationNodes';

import {
  ClothSystemNode
} from '../nodes/physics/SoftBodyNodes';

import {
  ObjectDetectionNode,
  ImageClassificationNode,
  FeatureExtractionNode
} from '../nodes/ai/ComputerVisionNodes';

/**
 * 节点分类枚举
 */
export enum NodeCategory {
  MOTION_CAPTURE = 'motion_capture',
  ENTITY_MANAGEMENT = 'entity_management',
  COMPONENT_MANAGEMENT = 'component_management',
  TRANSFORM = 'transform',
  PHYSICS = 'physics',
  ANIMATION = 'animation',
  INPUT = 'input',
  AUDIO = 'audio',
  SCENE_GENERATION = 'scene_generation',
  WATER_SYSTEM = 'water_system',
  PARTICLE_SYSTEM = 'particle_system',
  POST_PROCESS = 'post_process',
  TERRAIN_SYSTEM = 'terrain_system',
  BLOCKCHAIN = 'blockchain',
  LEARNING_RECORD = 'learning_record',
  UI_INTERFACE = 'ui_interface',
  RAG_APPLICATION = 'rag_application',
  SPATIAL_INFORMATION = 'spatial_information',
  NETWORK = 'network',
  RENDERING = 'rendering',
  ADVANCED_ANIMATION = 'advanced_animation',
  INDUSTRIAL_AUTOMATION = 'industrial_automation',
  ADVANCED_AUDIO = 'advanced_audio',
  VR_INPUT = 'vr_input',
  RENDERING_OPTIMIZATION = 'rendering_optimization',
  SOFT_BODY_PHYSICS = 'soft_body_physics',
  COMPUTER_VISION = 'computer_vision'
}

/**
 * 节点信息接口
 */
export interface NodeInfo {
  type: string;
  name: string;
  description: string;
  category: NodeCategory;
  nodeClass: typeof VisualScriptNode;
  icon?: string;
  color?: string;
  tags?: string[];
  deprecated?: boolean;
  experimental?: boolean;
}

/**
 * 节点注册表
 */
class NodeRegistryManager {
  private nodes: Map<string, NodeInfo> = new Map();
  private categories: Map<NodeCategory, NodeInfo[]> = new Map();
  private initialized: boolean = false;

  /**
   * 初始化节点注册表
   */
  initialize(): void {
    if (this.initialized) {
      return;
    }

    this.registerAllNodes();
    this.buildCategoryIndex();
    this.initialized = true;

    Debug.log('NodeRegistry', `节点注册完成: ${this.nodes.size}个节点, ${this.categories.size}个分类`);
  }

  /**
   * 注册所有节点
   */
  private registerAllNodes(): void {
    // 动作捕捉节点
    this.registerNode({
      type: CameraInputNode.TYPE,
      name: CameraInputNode.NAME,
      description: CameraInputNode.DESCRIPTION,
      category: NodeCategory.MOTION_CAPTURE,
      nodeClass: CameraInputNode,
      icon: 'camera',
      color: '#FF6B6B',
      tags: ['camera', 'input', 'video']
    });

    this.registerNode({
      type: PoseDetectionNode.TYPE,
      name: PoseDetectionNode.NAME,
      description: PoseDetectionNode.DESCRIPTION,
      category: NodeCategory.MOTION_CAPTURE,
      nodeClass: PoseDetectionNode,
      icon: 'person',
      color: '#FF6B6B',
      tags: ['pose', 'detection', 'mediapipe']
    });

    this.registerNode({
      type: HandTrackingNode.TYPE,
      name: HandTrackingNode.NAME,
      description: HandTrackingNode.DESCRIPTION,
      category: NodeCategory.MOTION_CAPTURE,
      nodeClass: HandTrackingNode,
      icon: 'hand',
      color: '#FF6B6B',
      tags: ['hand', 'tracking', 'gesture']
    });

    this.registerNode({
      type: VirtualInteractionNode.TYPE,
      name: VirtualInteractionNode.NAME,
      description: VirtualInteractionNode.DESCRIPTION,
      category: NodeCategory.MOTION_CAPTURE,
      nodeClass: VirtualInteractionNode,
      icon: 'touch',
      color: '#FF6B6B',
      tags: ['virtual', 'interaction', 'mapping']
    });

    this.registerNode({
      type: FaceDetectionNode.TYPE,
      name: FaceDetectionNode.NAME,
      description: FaceDetectionNode.DESCRIPTION,
      category: NodeCategory.MOTION_CAPTURE,
      nodeClass: FaceDetectionNode,
      icon: 'face',
      color: '#FF6B6B',
      tags: ['face', 'detection', 'expression']
    });

    this.registerNode({
      type: BodyAnalysisNode.TYPE,
      name: BodyAnalysisNode.NAME,
      description: BodyAnalysisNode.DESCRIPTION,
      category: NodeCategory.MOTION_CAPTURE,
      nodeClass: BodyAnalysisNode,
      icon: 'body',
      color: '#FF6B6B',
      tags: ['body', 'analysis', 'skeleton']
    });

    this.registerNode({
      type: MotionProcessingNode.TYPE,
      name: MotionProcessingNode.NAME,
      description: MotionProcessingNode.DESCRIPTION,
      category: NodeCategory.MOTION_CAPTURE,
      nodeClass: MotionProcessingNode,
      icon: 'process',
      color: '#FF6B6B',
      tags: ['motion', 'processing', 'filter']
    });

    // 实体管理节点
    this.registerNode({
      type: CreateEntityNode.TYPE,
      name: CreateEntityNode.NAME,
      description: CreateEntityNode.DESCRIPTION,
      category: NodeCategory.ENTITY_MANAGEMENT,
      nodeClass: CreateEntityNode,
      icon: 'add_box',
      color: '#4ECDC4',
      tags: ['entity', 'create', 'object']
    });

    this.registerNode({
      type: DestroyEntityNode.TYPE,
      name: DestroyEntityNode.NAME,
      description: DestroyEntityNode.DESCRIPTION,
      category: NodeCategory.ENTITY_MANAGEMENT,
      nodeClass: DestroyEntityNode,
      icon: 'delete',
      color: '#4ECDC4',
      tags: ['entity', 'destroy', 'remove']
    });

    this.registerNode({
      type: FindEntityNode.TYPE,
      name: FindEntityNode.NAME,
      description: FindEntityNode.DESCRIPTION,
      category: NodeCategory.ENTITY_MANAGEMENT,
      nodeClass: FindEntityNode,
      icon: 'search',
      color: '#4ECDC4',
      tags: ['entity', 'find', 'query']
    });

    this.registerNode({
      type: CloneEntityNode.TYPE,
      name: CloneEntityNode.NAME,
      description: CloneEntityNode.DESCRIPTION,
      category: NodeCategory.ENTITY_MANAGEMENT,
      nodeClass: CloneEntityNode,
      icon: 'content_copy',
      color: '#4ECDC4',
      tags: ['entity', 'clone', 'duplicate']
    });

    this.registerNode({
      type: EntityStateNode.TYPE,
      name: EntityStateNode.NAME,
      description: EntityStateNode.DESCRIPTION,
      category: NodeCategory.ENTITY_MANAGEMENT,
      nodeClass: EntityStateNode,
      icon: 'info',
      color: '#4ECDC4',
      tags: ['entity', 'state', 'status']
    });

    // 网络通信节点
    this.registerNode({
      type: WebSocketNode.TYPE,
      name: WebSocketNode.NAME,
      description: WebSocketNode.DESCRIPTION,
      category: NodeCategory.NETWORK,
      nodeClass: WebSocketNode,
      icon: 'wifi',
      color: '#9B59B6',
      tags: ['websocket', 'realtime', 'communication']
    });

    this.registerNode({
      type: WebRTCNode.TYPE,
      name: WebRTCNode.NAME,
      description: WebRTCNode.DESCRIPTION,
      category: NodeCategory.NETWORK,
      nodeClass: WebRTCNode,
      icon: 'video_call',
      color: '#9B59B6',
      tags: ['webrtc', 'p2p', 'video', 'audio']
    });

    this.registerNode({
      type: HTTPRequestNode.TYPE,
      name: HTTPRequestNode.NAME,
      description: HTTPRequestNode.DESCRIPTION,
      category: NodeCategory.NETWORK,
      nodeClass: HTTPRequestNode,
      icon: 'http',
      color: '#9B59B6',
      tags: ['http', 'api', 'request']
    });

    this.registerNode({
      type: NetworkSyncNode.TYPE,
      name: NetworkSyncNode.NAME,
      description: NetworkSyncNode.DESCRIPTION,
      category: NodeCategory.NETWORK,
      nodeClass: NetworkSyncNode,
      icon: 'sync',
      color: '#9B59B6',
      tags: ['sync', 'multiplayer', 'state']
    });

    // 渲染系统节点
    this.registerNode({
      type: MaterialSystemNode.TYPE,
      name: MaterialSystemNode.NAME,
      description: MaterialSystemNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: MaterialSystemNode,
      icon: 'palette',
      color: '#E67E22',
      tags: ['material', 'shader', 'pbr']
    });

    this.registerNode({
      type: LightControlNode.TYPE,
      name: LightControlNode.NAME,
      description: LightControlNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: LightControlNode,
      icon: 'lightbulb',
      color: '#E67E22',
      tags: ['light', 'illumination', 'shadow']
    });

    this.registerNode({
      type: CameraManagerNode.TYPE,
      name: CameraManagerNode.NAME,
      description: CameraManagerNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: CameraManagerNode,
      icon: 'videocam',
      color: '#E67E22',
      tags: ['camera', 'view', 'perspective']
    });

    this.registerNode({
      type: RenderConfigNode.TYPE,
      name: RenderConfigNode.NAME,
      description: RenderConfigNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: RenderConfigNode,
      icon: 'settings',
      color: '#E67E22',
      tags: ['render', 'quality', 'config']
    });

    // 高级动画节点
    this.registerNode({
      type: AnimationStateMachineNode.TYPE,
      name: AnimationStateMachineNode.NAME,
      description: AnimationStateMachineNode.DESCRIPTION,
      category: NodeCategory.ADVANCED_ANIMATION,
      nodeClass: AnimationStateMachineNode,
      icon: 'state_machine',
      color: '#8E44AD',
      tags: ['animation', 'state', 'transition']
    });

    this.registerNode({
      type: AnimationBlendNode.TYPE,
      name: AnimationBlendNode.NAME,
      description: AnimationBlendNode.DESCRIPTION,
      category: NodeCategory.ADVANCED_ANIMATION,
      nodeClass: AnimationBlendNode,
      icon: 'blend',
      color: '#8E44AD',
      tags: ['animation', 'blend', 'mix']
    });

    this.registerNode({
      type: IKSystemNode.TYPE,
      name: IKSystemNode.NAME,
      description: IKSystemNode.DESCRIPTION,
      category: NodeCategory.ADVANCED_ANIMATION,
      nodeClass: IKSystemNode,
      icon: 'skeleton',
      color: '#8E44AD',
      tags: ['ik', 'inverse', 'kinematics']
    });

    this.registerNode({
      type: AnimationEventNode.TYPE,
      name: AnimationEventNode.NAME,
      description: AnimationEventNode.DESCRIPTION,
      category: NodeCategory.ADVANCED_ANIMATION,
      nodeClass: AnimationEventNode,
      icon: 'event',
      color: '#8E44AD',
      tags: ['animation', 'event', 'callback']
    });

    // 工业自动化节点
    this.registerNode({
      type: DeviceManagerNode.TYPE,
      name: DeviceManagerNode.NAME,
      description: DeviceManagerNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: DeviceManagerNode,
      icon: 'device_hub',
      color: '#795548',
      tags: ['device', 'industrial', 'automation']
    });

    this.registerNode({
      type: DataCollectionNode.TYPE,
      name: DataCollectionNode.NAME,
      description: DataCollectionNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: DataCollectionNode,
      icon: 'data_usage',
      color: '#795548',
      tags: ['data', 'collection', 'industrial']
    });

    this.registerNode({
      type: QualityInspectionNode.TYPE,
      name: QualityInspectionNode.NAME,
      description: QualityInspectionNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: QualityInspectionNode,
      icon: 'quality_check',
      color: '#795548',
      tags: ['quality', 'inspection', 'defect']
    });

    this.registerNode({
      type: AlarmSystemNode.TYPE,
      name: AlarmSystemNode.NAME,
      description: AlarmSystemNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: AlarmSystemNode,
      icon: 'alarm',
      color: '#795548',
      tags: ['alarm', 'notification', 'alert']
    });

    this.registerNode({
      type: ProcessControlNode.TYPE,
      name: ProcessControlNode.NAME,
      description: ProcessControlNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: ProcessControlNode,
      icon: 'settings',
      color: '#795548',
      tags: ['process', 'control', 'workflow']
    });

    // 高级音频节点
    this.registerNode({
      type: SpatialAudioNode.TYPE,
      name: SpatialAudioNode.NAME,
      description: SpatialAudioNode.DESCRIPTION,
      category: NodeCategory.ADVANCED_AUDIO,
      nodeClass: SpatialAudioNode,
      icon: 'surround_sound',
      color: '#FF5722',
      tags: ['spatial', 'audio', '3d', 'sound']
    });

    this.registerNode({
      type: AudioFilterNode.TYPE,
      name: AudioFilterNode.NAME,
      description: AudioFilterNode.DESCRIPTION,
      category: NodeCategory.ADVANCED_AUDIO,
      nodeClass: AudioFilterNode,
      icon: 'equalizer',
      color: '#FF5722',
      tags: ['audio', 'filter', 'frequency']
    });

    this.registerNode({
      type: AudioEffectNode.TYPE,
      name: AudioEffectNode.NAME,
      description: AudioEffectNode.DESCRIPTION,
      category: NodeCategory.ADVANCED_AUDIO,
      nodeClass: AudioEffectNode,
      icon: 'graphic_eq',
      color: '#FF5722',
      tags: ['audio', 'effect', 'reverb', 'delay']
    });

    // VR输入节点
    this.registerNode({
      type: VRControllerNode.TYPE,
      name: VRControllerNode.NAME,
      description: VRControllerNode.DESCRIPTION,
      category: NodeCategory.VR_INPUT,
      nodeClass: VRControllerNode,
      icon: 'gamepad',
      color: '#3F51B5',
      tags: ['vr', 'controller', 'input', 'xr']
    });

    this.registerNode({
      type: GestureRecognitionNode.TYPE,
      name: GestureRecognitionNode.NAME,
      description: GestureRecognitionNode.DESCRIPTION,
      category: NodeCategory.VR_INPUT,
      nodeClass: GestureRecognitionNode,
      icon: 'pan_tool',
      color: '#3F51B5',
      tags: ['gesture', 'hand', 'recognition']
    });

    this.registerNode({
      type: VoiceRecognitionNode.TYPE,
      name: VoiceRecognitionNode.NAME,
      description: VoiceRecognitionNode.DESCRIPTION,
      category: NodeCategory.VR_INPUT,
      nodeClass: VoiceRecognitionNode,
      icon: 'mic',
      color: '#3F51B5',
      tags: ['voice', 'speech', 'recognition']
    });

    // 渲染优化节点
    this.registerNode({
      type: LODSystemNode.TYPE,
      name: LODSystemNode.NAME,
      description: LODSystemNode.DESCRIPTION,
      category: NodeCategory.RENDERING_OPTIMIZATION,
      nodeClass: LODSystemNode,
      icon: 'layers',
      color: '#607D8B',
      tags: ['lod', 'optimization', 'performance']
    });

    this.registerNode({
      type: BatchRenderingNode.TYPE,
      name: BatchRenderingNode.NAME,
      description: BatchRenderingNode.DESCRIPTION,
      category: NodeCategory.RENDERING_OPTIMIZATION,
      nodeClass: BatchRenderingNode,
      icon: 'view_module',
      color: '#607D8B',
      tags: ['batch', 'rendering', 'optimization']
    });

    this.registerNode({
      type: InstancedRenderingNode.TYPE,
      name: InstancedRenderingNode.NAME,
      description: InstancedRenderingNode.DESCRIPTION,
      category: NodeCategory.RENDERING_OPTIMIZATION,
      nodeClass: InstancedRenderingNode,
      icon: 'content_copy',
      color: '#607D8B',
      tags: ['instanced', 'rendering', 'performance']
    });

    this.registerNode({
      type: FrustumCullingNode.TYPE,
      name: FrustumCullingNode.NAME,
      description: FrustumCullingNode.DESCRIPTION,
      category: NodeCategory.RENDERING_OPTIMIZATION,
      nodeClass: FrustumCullingNode,
      icon: 'visibility_off',
      color: '#607D8B',
      tags: ['culling', 'frustum', 'optimization']
    });

    // 软体物理节点
    this.registerNode({
      type: ClothSystemNode.TYPE,
      name: ClothSystemNode.NAME,
      description: ClothSystemNode.DESCRIPTION,
      category: NodeCategory.SOFT_BODY_PHYSICS,
      nodeClass: ClothSystemNode,
      icon: 'texture',
      color: '#9C27B0',
      tags: ['cloth', 'soft', 'physics', 'simulation']
    });

    // 计算机视觉节点
    this.registerNode({
      type: ObjectDetectionNode.TYPE,
      name: ObjectDetectionNode.NAME,
      description: ObjectDetectionNode.DESCRIPTION,
      category: NodeCategory.COMPUTER_VISION,
      nodeClass: ObjectDetectionNode,
      icon: 'search',
      color: '#FF9800',
      tags: ['object', 'detection', 'ai', 'vision']
    });

    this.registerNode({
      type: ImageClassificationNode.TYPE,
      name: ImageClassificationNode.NAME,
      description: ImageClassificationNode.DESCRIPTION,
      category: NodeCategory.COMPUTER_VISION,
      nodeClass: ImageClassificationNode,
      icon: 'category',
      color: '#FF9800',
      tags: ['image', 'classification', 'ai']
    });

    this.registerNode({
      type: FeatureExtractionNode.TYPE,
      name: FeatureExtractionNode.NAME,
      description: FeatureExtractionNode.DESCRIPTION,
      category: NodeCategory.COMPUTER_VISION,
      nodeClass: FeatureExtractionNode,
      icon: 'scatter_plot',
      color: '#FF9800',
      tags: ['feature', 'extraction', 'keypoint']
    });
  }

  /**
   * 注册单个节点
   */
  private registerNode(nodeInfo: NodeInfo): void {
    this.nodes.set(nodeInfo.type, nodeInfo);
  }

  /**
   * 构建分类索引
   */
  private buildCategoryIndex(): void {
    this.categories.clear();

    for (const nodeInfo of this.nodes.values()) {
      if (!this.categories.has(nodeInfo.category)) {
        this.categories.set(nodeInfo.category, []);
      }
      this.categories.get(nodeInfo.category)!.push(nodeInfo);
    }

    // 对每个分类的节点进行排序
    for (const nodes of this.categories.values()) {
      nodes.sort((a, b) => a.name.localeCompare(b.name));
    }
  }

  /**
   * 获取节点信息
   */
  getNodeInfo(type: string): NodeInfo | undefined {
    return this.nodes.get(type);
  }

  /**
   * 获取所有节点
   */
  getAllNodes(): NodeInfo[] {
    return Array.from(this.nodes.values());
  }

  /**
   * 根据分类获取节点
   */
  getNodesByCategory(category: NodeCategory): NodeInfo[] {
    return this.categories.get(category) || [];
  }

  /**
   * 搜索节点
   */
  searchNodes(query: string): NodeInfo[] {
    const lowerQuery = query.toLowerCase();
    return Array.from(this.nodes.values()).filter(nodeInfo => 
      nodeInfo.name.toLowerCase().includes(lowerQuery) ||
      nodeInfo.description.toLowerCase().includes(lowerQuery) ||
      nodeInfo.tags?.some(tag => tag.toLowerCase().includes(lowerQuery))
    );
  }

  /**
   * 创建节点实例
   */
  createNode(type: string, id?: string): VisualScriptNode | null {
    const nodeInfo = this.nodes.get(type);
    if (!nodeInfo) {
      Debug.warn('NodeRegistry', `未找到节点类型: ${type}`);
      return null;
    }

    try {
      return new nodeInfo.nodeClass(type, nodeInfo.name, id);
    } catch (error) {
      Debug.error('NodeRegistry', `节点创建失败: ${type}`, error);
      return null;
    }
  }

  /**
   * 获取分类信息
   */
  getCategoryInfo(): { category: NodeCategory; name: string; count: number }[] {
    const categoryNames: { [key in NodeCategory]: string } = {
      [NodeCategory.MOTION_CAPTURE]: '动作捕捉',
      [NodeCategory.ENTITY_MANAGEMENT]: '实体管理',
      [NodeCategory.COMPONENT_MANAGEMENT]: '组件管理',
      [NodeCategory.TRANSFORM]: '变换操作',
      [NodeCategory.PHYSICS]: '物理系统',
      [NodeCategory.ANIMATION]: '动画系统',
      [NodeCategory.INPUT]: '输入系统',
      [NodeCategory.AUDIO]: '音频系统',
      [NodeCategory.SCENE_GENERATION]: '场景生成',
      [NodeCategory.WATER_SYSTEM]: '水系统',
      [NodeCategory.PARTICLE_SYSTEM]: '粒子系统',
      [NodeCategory.POST_PROCESS]: '后处理',
      [NodeCategory.TERRAIN_SYSTEM]: '地形系统',
      [NodeCategory.BLOCKCHAIN]: '区块链',
      [NodeCategory.LEARNING_RECORD]: '学习记录',
      [NodeCategory.UI_INTERFACE]: 'UI界面',
      [NodeCategory.RAG_APPLICATION]: 'RAG应用',
      [NodeCategory.SPATIAL_INFORMATION]: '空间信息',
      [NodeCategory.NETWORK]: '网络通信',
      [NodeCategory.RENDERING]: '渲染系统',
      [NodeCategory.ADVANCED_ANIMATION]: '高级动画',
      [NodeCategory.INDUSTRIAL_AUTOMATION]: '工业自动化',
      [NodeCategory.ADVANCED_AUDIO]: '高级音频',
      [NodeCategory.VR_INPUT]: 'VR输入',
      [NodeCategory.RENDERING_OPTIMIZATION]: '渲染优化',
      [NodeCategory.SOFT_BODY_PHYSICS]: '软体物理',
      [NodeCategory.COMPUTER_VISION]: '计算机视觉'
    };

    return Array.from(this.categories.entries()).map(([category, nodes]) => ({
      category,
      name: categoryNames[category],
      count: nodes.length
    }));
  }

  /**
   * 获取统计信息
   */
  getStatistics(): {
    totalNodes: number;
    categoriesCount: number;
    nodesByCategory: { [key: string]: number };
  } {
    const nodesByCategory: { [key: string]: number } = {};
    
    for (const [category, nodes] of this.categories.entries()) {
      nodesByCategory[category] = nodes.length;
    }

    return {
      totalNodes: this.nodes.size,
      categoriesCount: this.categories.size,
      nodesByCategory
    };
  }
}

// 导出单例实例
export const NodeRegistry = new NodeRegistryManager();
